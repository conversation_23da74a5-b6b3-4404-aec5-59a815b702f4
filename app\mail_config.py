import os
from fastapi_mail import ConnectionConfig
from pydantic import SecretStr
from dotenv import load_dotenv

load_dotenv()

conf = ConnectionConfig(
    MAIL_USERNAME=os.getenv("POSTMARK_SERVER_TOKEN", "************************************"),
    MAIL_PASSWORD=SecretStr(os.getenv("POSTMARK_SERVER_TOKEN", "************************************")),
    MAIL_FROM="<EMAIL>",
    MAIL_PORT=587,
    MAIL_SERVER="smtp.postmarkapp.com",
    MAIL_STARTTLS=True,
    MAIL_SSL_TLS=False,
    USE_CREDENTIALS=True,
    VALIDATE_CERTS=True
)



<div class="mycoolinput" data-signals="{_{{ namealwayschange }}isPasswordBOOL: true}" >

  <input data-attr-type="$_{{ namealwayschange }}isPasswordBOOL ? 'password' : 'text'"
         pattern="((?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\W]).{8,64})"
         placeholder=" "
         {% if value %}value="{{ value }}"{% endif %}
         name="{{ name | default(namealwayschange) }}"
         id="{{ namealwayschange }}"
         required />

  <label class="mycoolinputslabel" data-attr-disabled="$_{{ namealwayschange }}isPasswordBOOL">
    {{ label | safe }}
  </label>

  <span class="input-group__error">Password invalid
    <button type="button" onclick="Swal.fire({
      title: 'Password Requirements',
      html: 'The password should:<ul style=&quot;text-align: left; margin: 16px 0;&quot;><li>Be 8 to 64 characters long</li><li>Contain at least one digit</li><li>Contain at least one lowercase letter</li><li>Contain at least one uppercase letter</li><li>Contain at least one special character</li></ul>',
      icon: 'info',
      confirmButtonText: 'Got it',
      backdrop: false,
      customClass: {
        popup: 'custom-swal-popup',
        title: 'custom-swal-title',
        htmlContainer: 'custom-swal-text',
        confirmButton: 'custom-swal-info-confirm'
      },
      showClass: {
        popup: 'swal2-show-custom'
      },
      hideClass: {
        popup: 'swal2-hide-custom'
      }
    })">
      <style>
          me {
              all: unset;
              background-color: transparent;
              color: var(--color-selected-red);
              width: 1.25rem;
              height: 1.25rem;
              margin-left: 0.5rem;
              text-align: center;
              cursor: pointer;
              transition: .25s ease;
              border: 1px solid var(--color-selected-red);
              border-radius: 50%;
              font-family: 'Noto Serif', serif;
              font-size: 0.875rem;
              font-weight: 500;
              line-height: 1.25rem;
              display: inline-block;
              position: relative;
              z-index: 4;
          }

          me:hover {
              background-color: var(--color-selected-red);
              color: var(--color-text-bright);
          }
      </style>
      i
    </button>
  </span>

  <style>
    .custom-swal-popup {
      font-family: 'Noto Sans', sans-serif;
      background-color: var(--color-background-bright);
      border: 2px solid var(--color-input-lines);
      border-radius: 16px;
    }

    .custom-swal-title {
      font-family: 'Noto Serif', serif;
      font-size: 24px;
      font-weight: 600;
      color: var(--color-text-black);
    }

    .custom-swal-text {
      font-family: 'Noto Sans', sans-serif;
      font-size: 18px;
      font-weight: 400;
      color: var(--color-text-black);
    }

    .custom-swal-info-confirm {
      font-family: 'Noto Sans', sans-serif;
      font-size: 16px;
      font-weight: 500;
      border-radius: 6px;
      padding: 12px 24px;
      background-color: var(--color-background-dark);
      color: var(--color-text-bright);
      border: 1px solid var(--color-text-dark);
    }

    .custom-swal-info-confirm:hover {
      background-color: var(--color-background-middle);
    }

    .swal2-icon.swal2-info {
      border-color: var(--color-text-dark);
      color: var(--color-text-dark);
    }

    /* Custom subtle popup animation */
    .swal2-show-custom {
      animation: swal2-show-custom 400ms cubic-bezier(.63,.7,.16,1.28);
    }

    .swal2-hide-custom {
      animation: swal2-hide-custom 300ms ease-out;
    }

    @keyframes swal2-show-custom {
      0% {
        transform: scale(0.7);
        opacity: 0;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }

    @keyframes swal2-hide-custom {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      100% {
        transform: scale(0.8);
        opacity: 0;
      }
    }
  </style>

  <span class="fa-regular fa-eye" data-on-click="$_{{ namealwayschange }}isPasswordBOOL=!$_{{ namealwayschange }}isPasswordBOOL" data-class-fa-eye="$_{{ namealwayschange }}isPasswordBOOL" data-class-fa-eye-slash="!$_{{ namealwayschange }}isPasswordBOOL">
    <style>
        me {
            color: var(--color-input-lines);
            font-size: 18px;
            position: absolute;
            right: 6px;
            top: 40px;
            z-index: 4;
        }
    </style>
  </span>

</div>
